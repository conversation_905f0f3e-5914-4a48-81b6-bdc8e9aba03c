using Godot;

public partial class AnvilMenu : <PERSON><PERSON><PERSON><PERSON><PERSON>, IMenu
{
	private AnimationPlayer _animationPlayer;
	private Button _closeButton;
	private Button _plankCraftButton;
	private Button _beamCraftButton;
	private Button _stickCraftButton;
	private Button _woodenKeyCraftButton;
	private Button _stoneBrickCraftButton;
	private Sprite2D _plankBuildButtonSprite;
	private Sprite2D _beamBuildButtonSprite;
	private Sprite2D _stickBuildButtonSprite;
	private Sprite2D _woodenKeyBuildButtonSprite;
	private Sprite2D _stoneBrickBuildButtonSprite;
	private Anvil _anvil;

	private const int PLANK_WOOD_REQUIRED = 2;
	private const int PLANK_STONE_REQUIRED = 2;
	private const int BEAM_PLANK_REQUIRED = 2;
	private const int BEAM_STONE_REQUIRED = 2;
	private const int STICK_WOOD_REQUIRED = 1;
	private const int WOODEN_KEY_WOOD_REQUIRED = 4;
	private const int WOODEN_KEY_PLANK_REQUIRED = 2;
	private const int STONE_BRICK_STONE_REQUIRED = 3;
	private const int STONE_BRICK_WOOD_REQUIRED = 1;

	public override void _Ready()
	{
		_animationPlayer = GetNode<AnimationPlayer>("AnimationPlayer");
		if (_animationPlayer == null)
		{
			GD.PrintErr("AnvilMenu: AnimationPlayer not found!");
			return;
		}

		_closeButton = GetNode<Button>("Control/Panel/CloseButton");
		if (_closeButton == null)
		{
			GD.PrintErr("AnvilMenu: Close button not found!");
			return;
		}

		_plankCraftButton = GetNode<Button>("Control/Panel/ScrollContainer/VBoxContainer/ItemListWoodenPlank/Button");
		if (_plankCraftButton == null)
		{
			GD.PrintErr("AnvilMenu: Plank craft button not found!");
			return;
		}

		_beamCraftButton = GetNode<Button>("Control/Panel/ScrollContainer/VBoxContainer/ItemListWoodenBeam/Button");
		if (_beamCraftButton == null)
		{
			GD.PrintErr("AnvilMenu: Beam craft button not found!");
			return;
		}

		_stickCraftButton = GetNode<Button>("Control/Panel/ScrollContainer/VBoxContainer/ItemListWoodenStick/Button");
		if (_stickCraftButton == null)
		{
			GD.PrintErr("AnvilMenu: Stick craft button not found!");
			return;
		}

		_woodenKeyCraftButton = GetNode<Button>("Control/Panel/ScrollContainer/VBoxContainer/ItemListWoodenKey/Button");
		if (_woodenKeyCraftButton == null)
		{
			GD.PrintErr("AnvilMenu: Wooden Key craft button not found!");
			return;
		}

		_plankBuildButtonSprite = GetNode<Sprite2D>("Control/Panel/ScrollContainer/VBoxContainer/ItemListWoodenPlank/BuildButton");
		if (_plankBuildButtonSprite == null)
		{
			GD.PrintErr("AnvilMenu: Plank BuildButton sprite not found!");
			return;
		}

		_beamBuildButtonSprite = GetNode<Sprite2D>("Control/Panel/ScrollContainer/VBoxContainer/ItemListWoodenBeam/BuildButton");
		if (_beamBuildButtonSprite == null)
		{
			GD.PrintErr("AnvilMenu: Beam BuildButton sprite not found!");
			return;
		}

		_stickBuildButtonSprite = GetNode<Sprite2D>("Control/Panel/ScrollContainer/VBoxContainer/ItemListWoodenStick/BuildButton");
		if (_stickBuildButtonSprite == null)
		{
			GD.PrintErr("AnvilMenu: Stick BuildButton sprite not found!");
			return;
		}

		_woodenKeyBuildButtonSprite = GetNode<Sprite2D>("Control/Panel/ScrollContainer/VBoxContainer/ItemListWoodenKey/BuildButton");
		if (_woodenKeyBuildButtonSprite == null)
		{
			GD.PrintErr("AnvilMenu: Wooden Key BuildButton sprite not found!");
			return;
		}

		_stoneBrickCraftButton = GetNode<Button>("Control/Panel/ScrollContainer/VBoxContainer/ItemListStoneBrick/Button");
		if (_stoneBrickCraftButton == null)
		{
			GD.PrintErr("AnvilMenu: Stone Brick craft button not found!");
			return;
		}

		_stoneBrickBuildButtonSprite = GetNode<Sprite2D>("Control/Panel/ScrollContainer/VBoxContainer/ItemListStoneBrick/BuildButton");
		if (_stoneBrickBuildButtonSprite == null)
		{
			GD.PrintErr("AnvilMenu: Stone Brick BuildButton sprite not found!");
			return;
		}

		_closeButton.Pressed += OnCloseButtonPressed;
		_plankCraftButton.Pressed += OnPlankCraftButtonPressed;
		_beamCraftButton.Pressed += OnBeamCraftButtonPressed;
		_stickCraftButton.Pressed += OnStickCraftButtonPressed;
		_woodenKeyCraftButton.Pressed += OnWoodenKeyCraftButtonPressed;
		_stoneBrickCraftButton.Pressed += OnStoneBrickCraftButtonPressed;

		// Register with MenuManager
		MenuManager.Instance?.RegisterMenu("AnvilMenu", this);
	}

	public void SetAnvil(Anvil anvil)
	{
		_anvil = anvil;
	}

	public void OpenMenu()
	{
		UpdateAllCraftButtonStates();

		// Disable player movement when menu opens
		CommonSignals.Instance?.EmitPlayerMovementEnabled(false);

		if (_animationPlayer != null)
		{
			_animationPlayer.Play("Open");
		}
	}

	private void OnCloseButtonPressed()
	{
		// Use MenuManager to close the menu properly
		if (MenuManager.Instance != null)
		{
			MenuManager.Instance.CloseMenu("AnvilMenu");
		}
		else
		{
			CloseMenu();
		}
	}

	public void CloseMenu()
	{
		// Re-enable player movement when menu closes
		CommonSignals.Instance?.EmitPlayerMovementEnabled(true);

		if (_animationPlayer != null)
		{
			_animationPlayer.Play("Close");
		}
	}

	public bool IsMenuOpen()
	{
		return GetNode<Sprite2D>("Control/Panel").Visible;
	}

	private void OnPlankCraftButtonPressed()
	{
		if (!CanAffordPlank())
		{
			GD.Print("AnvilMenu: Not enough resources to craft plank!");
			return;
		}

		// Use MenuManager to close the menu properly
		if (MenuManager.Instance != null)
		{
			MenuManager.Instance.CloseMenu("AnvilMenu");
		}
		else
		{
			CloseMenu();
		}

		if (_anvil != null)
		{
			_anvil.StartCraftingPlank();
		}
		else
		{
			GD.PrintErr("AnvilMenu: Anvil reference not set!");
		}
	}

	private void OnBeamCraftButtonPressed()
	{
		if (!CanAffordBeam())
		{
			GD.Print("AnvilMenu: Not enough resources to craft beam!");
			return;
		}

		// Use MenuManager to close the menu properly
		if (MenuManager.Instance != null)
		{
			MenuManager.Instance.CloseMenu("AnvilMenu");
		}
		else
		{
			CloseMenu();
		}

		if (_anvil != null)
		{
			_anvil.StartCraftingBeam();
		}
		else
		{
			GD.PrintErr("AnvilMenu: Anvil reference not set!");
		}
	}

	private void OnStickCraftButtonPressed()
	{
		if (!CanAffordStick())
		{
			GD.Print("AnvilMenu: Not enough resources to craft stick!");
			return;
		}

		// Use MenuManager to close the menu properly
		if (MenuManager.Instance != null)
		{
			MenuManager.Instance.CloseMenu("AnvilMenu");
		}
		else
		{
			CloseMenu();
		}

		if (_anvil != null)
		{
			_anvil.StartCraftingStick();
		}
		else
		{
			GD.PrintErr("AnvilMenu: Anvil reference not set!");
		}
	}

	private void OnWoodenKeyCraftButtonPressed()
	{
		if (!CanAffordWoodenKey())
		{
			GD.Print("AnvilMenu: Not enough resources to craft wooden key!");
			return;
		}

		// Use MenuManager to close the menu properly
		if (MenuManager.Instance != null)
		{
			MenuManager.Instance.CloseMenu("AnvilMenu");
		}
		else
		{
			CloseMenu();
		}

		if (_anvil != null)
		{
			_anvil.StartCraftingWoodenKey();
		}
		else
		{
			GD.PrintErr("AnvilMenu: Anvil reference not set!");
		}
	}

	private void OnStoneBrickCraftButtonPressed()
	{
		if (!CanAffordStoneBrick())
		{
			GD.Print("AnvilMenu: Not enough resources to craft stone brick!");
			return;
		}

		// Use MenuManager to close the menu properly
		if (MenuManager.Instance != null)
		{
			MenuManager.Instance.CloseMenu("AnvilMenu");
		}
		else
		{
			CloseMenu();
		}

		if (_anvil != null)
		{
			_anvil.StartCraftingStoneBrick();
		}
		else
		{
			GD.PrintErr("AnvilMenu: Anvil reference not set!");
		}
	}

	private bool CanAffordPlank()
	{
		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager == null) return false;

		return resourcesManager.HasResource(ResourceType.Wood, PLANK_WOOD_REQUIRED) &&
			   resourcesManager.HasResource(ResourceType.Stone, PLANK_STONE_REQUIRED);
	}

	private bool CanAffordBeam()
	{
		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager == null) return false;

		return resourcesManager.HasResource(ResourceType.Plank, BEAM_PLANK_REQUIRED) &&
			   resourcesManager.HasResource(ResourceType.Stone, BEAM_STONE_REQUIRED);
	}

	private bool CanAffordStick()
	{
		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager == null) return false;

		return resourcesManager.HasResource(ResourceType.Wood, STICK_WOOD_REQUIRED);
	}

	private bool CanAffordWoodenKey()
	{
		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager == null) return false;

		return resourcesManager.HasResource(ResourceType.Wood, WOODEN_KEY_WOOD_REQUIRED) &&
			   resourcesManager.HasResource(ResourceType.Plank, WOODEN_KEY_PLANK_REQUIRED);
	}

	private void UpdateAllCraftButtonStates()
	{
		UpdateCraftButtonState(_plankBuildButtonSprite, _plankCraftButton, CanAffordPlank());
		UpdateCraftButtonState(_beamBuildButtonSprite, _beamCraftButton, CanAffordBeam());
		UpdateCraftButtonState(_stickBuildButtonSprite, _stickCraftButton, CanAffordStick());
		UpdateCraftButtonState(_woodenKeyBuildButtonSprite, _woodenKeyCraftButton, CanAffordWoodenKey());
	}

	private void UpdateCraftButtonState(Sprite2D buildButtonSprite, Button craftButton, bool canAfford)
	{
		if (buildButtonSprite != null)
		{
			if (canAfford)
			{
				buildButtonSprite.Modulate = Colors.White;
			}
			else
			{
				buildButtonSprite.Modulate = new Color(0.6f, 0.6f, 0.6f, 1.0f);
			}
		}

		if (craftButton != null)
		{
			craftButton.Disabled = !canAfford;
		}
	}



	public override void _ExitTree()
	{
		if (_closeButton != null)
		{
			_closeButton.Pressed -= OnCloseButtonPressed;
		}
		if (_plankCraftButton != null)
		{
			_plankCraftButton.Pressed -= OnPlankCraftButtonPressed;
		}
		if (_beamCraftButton != null)
		{
			_beamCraftButton.Pressed -= OnBeamCraftButtonPressed;
		}
		if (_stickCraftButton != null)
		{
			_stickCraftButton.Pressed -= OnStickCraftButtonPressed;
		}
		if (_woodenKeyCraftButton != null)
		{
			_woodenKeyCraftButton.Pressed -= OnWoodenKeyCraftButtonPressed;
		}
	}
}
