using Godot;

[System.Serializable]
public enum ResourceType
{
	None = 0,
	<PERSON> = 1,
	<PERSON> = 2,
	Net = 3,
	<PERSON>k = 4,
	<PERSON><PERSON> = 5,
	<PERSON> = 6,
	<PERSON> = 7,
	<PERSON> = 8,

	CopperOre = 9,
	IronOre = 10,
	GoldOre = 11,
	IndigosiumOre = 12,
	MithrilOre = 13,
	ErithrydiumOre = 14,
	AdamantiteOre = 15,
	UraniumOre = 16,

	CopperBar = 17,
	IronBar = 18,
	GoldBar = 19,
	IndigosiumBar = 20,
	MithrilBar = 21,
	ErithrydiumBar = 22,
	AdamantiteBar = 23,
	UraniumBar = 24,

	CopperSheet = 25,
	IronSheet = 26,
	GoldSheet = 27,
	IndigosiumSheet = 28,
	MithrilSheet = 29,
	ErithrydiumSheet = 30,
	AdamantiteSheet = 31,
	UraniumSheet = 32,

	WoodenBeam = 33,
	WoodenStick = 34,
	RawRabbitLeg = 35,
	CookedRabbitLeg = 36,
	<PERSON>enKey = 37,
	StoneBrick = 38,
	Stone2Brick = 39,
	<PERSON>ils = 40,
	<PERSON> = 41,
	CopperKey = 42,
	IronKey = 43,
	<PERSON><PERSON>ey = 44,
	IndigosiumKey = 45,
	Mithril<PERSON>ey = 46,
	E<PERSON>rydiumKey = 47,
	AdamantiteKey = 48,
	UraniumKey = 49,
	Charcoal = 50,

	BrownMushroom = 51,
	BlueMushroom = 52,
	RedMushroom = 53,
	VioletMushroom = 54,

	// Plants (55-99) - ordered to match sprite sheet rows
	Carrot = 55,
	Turnip = 56,
	Pumpkin = 57,
	Potato = 58,
	Onion = 59,
	Strawberry = 60,
	Cauliflower = 61,
	Tomato = 62,
	Parsnip = 63,
	SnapPeas = 64,
	Garlic = 65,
	Radish = 66,
	Corn = 67,
	Leek = 68,
	Wheat = 69,
	Sunflower = 70,
	Beetroot = 71,
	Cabbage = 72,
	RedCabbage = 73,
	Broccoli = 74,
	BrusselsSprout = 75,
	RedBellPepper = 76,
	Spinach = 77,
	BokChoy = 78,
	Artichoke = 79,
	Cotton = 80,
	PurpleGrapes = 81,
	GreenGrapes = 82,
	RedGrapes = 83,
	PinkGrapes = 84,
	Cantaloupe = 85,
	Honeydew = 86,
	ButternutSquash = 87,
	Buckwheat = 88,
	YellowBellPepper = 89,
	OrangeBellPepper = 90,
	PurpleBellPepper = 91,
	WhiteBellPepper = 92,
	Coffee = 93,
	Amaranth = 94,
	GlassGemCorn = 95,
	GreenChilliPepper = 96,
	RedChilliPepper = 97,
	YellowChilliPepper = 98,
	OrangeChilliPepper = 99,
	PurpleChilliPepper = 100,

	// Seed Bags (101-148) - corresponding to plants above
	CarrotSeedBag = 101,
	TurnipSeedBag = 102,
	PumpkinSeedBag = 103,
	PotatoSeedBag = 104,
	OnionSeedBag = 105,
	StrawberrySeedBag = 106,
	CauliflowerSeedBag = 107,
	TomatoSeedBag = 108,
	ParsnipSeedBag = 109,
	SnapPeasSeedBag = 110,
	GarlicSeedBag = 111,
	RadishSeedBag = 112,
	CornSeedBag = 113,
	LeekSeedBag = 114,
	WheatSeedBag = 115,
	SunflowerSeedBag = 116,
	BeetrootSeedBag = 117,
	CabbageSeedBag = 118,
	RedCabbageSeedBag = 119,
	BroccoliSeedBag = 120,
	BrusselsSproutSeedBag = 121,
	RedBellPepperSeedBag = 122,
	SpinachSeedBag = 123,
	BokChoySeedBag = 124,
	ArtichokeSeedBag = 125,
	CottonSeedBag = 126,
	PurpleGrapesSeedBag = 127,
	GreenGrapesSeedBag = 128,
	RedGrapesSeedBag = 129,
	PinkGrapesSeedBag = 130,
	CantaloupeSeedBag = 131,
	HoneydewSeedBag = 132,
	ButternutSquashSeedBag = 133,
	BuckwheatSeedBag = 134,
	YellowBellPepperSeedBag = 135,
	OrangeBellPepperSeedBag = 136,
	PurpleBellPepperSeedBag = 137,
	WhiteBellPepperSeedBag = 138,
	CoffeeSeedBag = 139,
	AmaranthSeedBag = 140,
	GlassGemCornSeedBag = 141,
	GreenChilliPepperSeedBag = 142,
	RedChilliPepperSeedBag = 143,
	YellowChilliPepperSeedBag = 144,
	OrangeChilliPepperSeedBag = 145,
	PurpleChilliPepperSeedBag = 146
}
