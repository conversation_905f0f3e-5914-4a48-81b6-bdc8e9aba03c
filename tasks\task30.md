Implement following tasks:

TASK-1: When rabbit gets hit by sword there is a hit animation (i mean rabbit is pushed). When he is pushed there are cases that he moves through colliders - that should not happen. Fix it so that rabbit does not move through colliders when he is hit and moves.

TASK-2: 
a) Add following resources to resource type: stone brick, stone2 brick (this is sand stone brick) and nails. 
b) Add to TextureManager these resources textures and appropriate icons - i will assign textures.
c) Add to ItemInformation details for these resources
d) Add translations from ItemInformation

TASK-3:
When I hit for example anvil by pickaxe - it gets hit and hp is reduced. There is also an animation when it gets hit. Appropriate progress bar is displayed when it gets hit.
I want to have similar behavior for rabbit campfire - currently when i hit it with pickaxe, it does nothing.

TASK-4:
In anvil menu, duplicate ItemListWoodenStick and call it ItemListStoneBrick. It should cost 3 stone and 1 wood to produce 1 stone brick. Handle it like all other resources that can be crafted there.
Then duplicate ItemList<PERSON>oodenKey and call ite ItemListStone2Brick. It should cost 3 stone2 and 1 wood to produce 1 stone2 brick. Handle it like all other resources that can be crafted there.

TASK-5:
Change price (in Build menu) of priducing Furnace1 to be 20 stone bricks and 20 stone2 bircks.
Then change Furnace2 to require 20 iron bars and 20 copper bars and 20 gold ore.
Then change Furnace3 to require 20 gold bars and 20 indigosium bars and 20 mithril ore.
Then change Furnace4 to require 20 mithril bars and 20 erithrydium bars and 20 adamantite ore.
I will adjust tscn (present proper prices), you should adjust cs script.

TASK-6:
Change prices (in build menu) of Grindstone - to be 20 wooden beam and 10 stone bricks and 10 stone2 bricks.